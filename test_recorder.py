#!/usr/bin/env python3
"""
Quick test of the ScreenRecorder functionality.
"""

from Recorder import ScreenRecorder
import time

def test_basic_recording():
    """Test basic recording functionality."""
    print("Testing ScreenRecorder...")
    
    # Create recorder with audio disabled for testing
    recorder = ScreenRecorder(
        fps=10.0,                    # Lower FPS for testing
        audio_enabled=False,         # Disable audio for simpler test
        show_mouse_clicks=True,      # Show mouse clicks
        show_preview=False           # No preview window
    )
    
    try:
        # Display recorder status
        status = recorder.get_recording_status()
        print(f"Screen size: {status['screen_size']}")
        print(f"FPS: {status['fps']}")
        print(f"Audio enabled: {status['audio_enabled']}")
        
        # Start recording
        print("\nStarting 3-second test recording...")
        success = recorder.start_recording("test_recording.mp4")
        
        if success:
            print("Recording started successfully!")
            
            # Record for 3 seconds
            for i in range(3):
                print(f"Recording... {i+1}/3 seconds")
                time.sleep(1)
            
            # Stop recording
            print("Stopping recording...")
            recorder.stop_recording()
            print("Test recording completed!")
            print("Check 'test_recording.mp4' file in the current directory.")
            
        else:
            print("Failed to start recording!")
            
    except Exception as e:
        print(f"Error during test: {e}")
        
    finally:
        # Clean up
        recorder.cleanup()
        print("Test cleanup completed.")

if __name__ == "__main__":
    test_basic_recording()
