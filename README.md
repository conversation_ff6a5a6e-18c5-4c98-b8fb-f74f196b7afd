# Screen Recorder

A comprehensive Python screen recording application with video and audio capture capabilities.

## Features

- **Full Screen Recording**: Capture the entire screen or specify a custom region
- **Multiple Output Formats**: Support for MP4 and AVI video formats
- **Audio Recording**: Optional audio capture with synchronized playback
- **Mouse Click Visualization**: Highlight mouse clicks during recording
- **Live Preview**: Optional real-time preview window
- **Pause/Resume**: Control recording with pause and resume functionality
- **Threading**: Non-blocking recording using separate threads
- **Error Handling**: Comprehensive error handling and logging
- **Context Manager**: Support for automatic resource cleanup

## Requirements

Install the required dependencies:

```bash
pip install -r requirements.txt
```

### Dependencies

- `opencv-python>=4.5.0` - Video processing and recording
- `numpy>=1.20.0` - Array operations for image processing
- `pyautogui>=0.9.50` - Screen capture functionality
- `pyaudio>=0.2.11` - Audio recording (optional)

## Quick Start

### Basic Usage

```python
from Recorder import ScreenRecorder

# Create a recorder
recorder = ScreenRecorder()

# Start recording
recorder.start_recording("my_recording.mp4")

# Record for 10 seconds
import time
time.sleep(10)

# Stop recording
recorder.stop_recording()

# Clean up
recorder.cleanup()
```

### Advanced Usage

```python
from Recorder import ScreenRecorder

# Create recorder with custom settings
recorder = ScreenRecorder(
    fps=30.0,                    # 30 FPS
    output_format="mp4",         # MP4 format
    video_codec="mp4v",          # MP4V codec
    audio_enabled=True,          # Enable audio
    show_mouse_clicks=True,      # Show mouse clicks
    show_preview=False           # No live preview
)

# Set recording region (left, top, width, height)
recorder.set_recording_region((100, 100, 800, 600))

# Start recording
recorder.start_recording("advanced_recording.mp4")

# Pause and resume
time.sleep(5)
recorder.pause_recording()
time.sleep(2)
recorder.resume_recording()
time.sleep(5)

# Stop and cleanup
recorder.stop_recording()
recorder.cleanup()
```

### Context Manager

```python
# Automatic cleanup with context manager
with ScreenRecorder(fps=25.0) as recorder:
    recorder.start_recording("context_recording.mp4")
    time.sleep(10)
    recorder.stop_recording()
```

## API Reference

### ScreenRecorder Class

#### Constructor Parameters

- `fps` (float): Frames per second for video recording (default: 20.0)
- `output_format` (str): Output video format - 'mp4' or 'avi' (default: 'mp4')
- `video_codec` (str): Video codec - 'mp4v', 'XVID', 'MJPG' (default: 'mp4v')
- `audio_enabled` (bool): Enable audio recording (default: True)
- `show_mouse_clicks` (bool): Highlight mouse clicks (default: True)
- `show_preview` (bool): Show live preview window (default: False)

#### Methods

- `start_recording(output_path=None)`: Start screen recording
- `stop_recording(save_audio=True)`: Stop recording and save files
- `pause_recording()`: Pause the current recording
- `resume_recording()`: Resume a paused recording
- `set_recording_region(region=None)`: Set custom recording area
- `get_recording_status()`: Get current recording status
- `cleanup()`: Clean up resources

## Examples

Run the example file to see various usage patterns:

```bash
python example_usage.py
```

This will create several example recordings demonstrating different features.

## Troubleshooting

### Audio Issues

If audio recording fails:
1. Ensure PyAudio is properly installed
2. Check microphone permissions
3. Try disabling audio: `ScreenRecorder(audio_enabled=False)`

### Video Issues

If video recording fails:
1. Ensure OpenCV is properly installed
2. Try different codecs: 'XVID', 'MJPG'
3. Check available disk space

### Performance Issues

For better performance:
1. Lower the FPS: `ScreenRecorder(fps=15.0)`
2. Disable preview: `show_preview=False`
3. Record smaller regions using `set_recording_region()`

## License

This project is open source and available under the MIT License.
