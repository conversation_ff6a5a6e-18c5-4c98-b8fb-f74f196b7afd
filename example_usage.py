#!/usr/bin/env python3
"""
Example usage of the ScreenRecorder class.
This file demonstrates various ways to use the screen recorder.
"""

from Recorder import ScreenRecorder
import time

def basic_recording_example():
    """Basic screen recording example."""
    print("=== Basic Recording Example ===")
    
    # Create a recorder with default settings
    recorder = ScreenRecorder()
    
    try:
        # Start recording
        print("Starting 5-second recording...")
        recorder.start_recording("basic_recording.mp4")
        
        # Record for 5 seconds
        time.sleep(5)
        
        # Stop recording
        recorder.stop_recording()
        print("Recording saved as 'basic_recording.mp4'")
        
    finally:
        recorder.cleanup()

def advanced_recording_example():
    """Advanced recording with custom settings."""
    print("\n=== Advanced Recording Example ===")
    
    # Create recorder with custom settings
    recorder = ScreenRecorder(
        fps=30.0,                    # Higher frame rate
        output_format="mp4",         # MP4 format
        video_codec="mp4v",          # MP4V codec
        audio_enabled=True,          # Enable audio recording
        show_mouse_clicks=True,      # Show mouse clicks
        show_preview=True            # Show live preview
    )
    
    try:
        # Set a custom recording region (left, top, width, height)
        # This will record only a portion of the screen
        # recorder.set_recording_region((100, 100, 800, 600))
        
        # Start recording with custom filename
        print("Starting advanced recording...")
        recorder.start_recording("advanced_recording.mp4")
        
        # Record for 8 seconds with pause/resume demonstration
        time.sleep(3)
        print("Pausing recording...")
        recorder.pause_recording()
        
        time.sleep(2)
        print("Resuming recording...")
        recorder.resume_recording()
        
        time.sleep(3)
        
        # Stop recording
        recorder.stop_recording()
        print("Advanced recording completed!")
        
        # Display recording status
        status = recorder.get_recording_status()
        print(f"Final status: {status}")
        
    finally:
        recorder.cleanup()

def context_manager_example():
    """Using the recorder as a context manager."""
    print("\n=== Context Manager Example ===")
    
    # Using with statement for automatic cleanup
    with ScreenRecorder(fps=25.0, show_mouse_clicks=False) as recorder:
        print("Recording with context manager...")
        recorder.start_recording("context_recording.mp4")
        time.sleep(4)
        recorder.stop_recording()
        print("Context manager recording completed!")

def region_recording_example():
    """Recording a specific region of the screen."""
    print("\n=== Region Recording Example ===")
    
    recorder = ScreenRecorder(fps=20.0)
    
    try:
        # Record only the center portion of the screen
        screen_size = recorder._get_screen_size()
        center_x = screen_size[0] // 4
        center_y = screen_size[1] // 4
        region_width = screen_size[0] // 2
        region_height = screen_size[1] // 2
        
        recorder.set_recording_region((center_x, center_y, region_width, region_height))
        
        print(f"Recording center region: {center_x}, {center_y}, {region_width}, {region_height}")
        recorder.start_recording("region_recording.mp4")
        
        time.sleep(5)
        
        recorder.stop_recording()
        print("Region recording completed!")
        
    finally:
        recorder.cleanup()

if __name__ == "__main__":
    print("ScreenRecorder Usage Examples")
    print("============================")
    
    try:
        # Run all examples
        basic_recording_example()
        advanced_recording_example()
        context_manager_example()
        region_recording_example()
        
        print("\n=== All Examples Completed ===")
        print("Check the generated video files:")
        print("- basic_recording.mp4")
        print("- advanced_recording.mp4")
        print("- context_recording.mp4")
        print("- region_recording.mp4")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"Error running examples: {e}")
