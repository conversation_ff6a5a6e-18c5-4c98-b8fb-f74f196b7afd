import cv2
import numpy as np
import pyautogui
import time
from datetime import datetime

# Screen recording settings
SCREEN_SIZE = pyautogui.size()  # Get screen size
FPS = 10.0  # Frames per second
OUTPUT_FILENAME = "screen_recording.mp4"  # Output file name
RECORD_DURATION = 10  # Duration of each recording loop in seconds

# Initialize video writer
fourcc = cv2.VideoWriter_fourcc(*"mp4v")  # Codec
out = cv2.VideoWriter(OUTPUT_FILENAME, fourcc, FPS, SCREEN_SIZE)

# Function to record screen and mouse clicks
def record_screen(duration):
    start_time = time.time()
    while (time.time() - start_time) < duration:
        # Capture screen
        img = pyautogui.screenshot()
        frame = np.array(img)
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

        # Get mouse position and draw a circle for clicks
        mouse_x, mouse_y = pyautogui.position()
        if pyautogui.mouseDown():
            cv2.circle(frame, (mouse_x, mouse_y), 10, (0, 0, 255), -1)  # Red circle for clicks

        # Write frame to video
        out.write(frame)

        # Display recording (optional)
        cv2.imshow("Screen Recording", frame)
        if cv2.waitKey(1) == ord("q"):  # Press 'q' to stop recording
            break

# Main loop to record repeatedly
try:
    while True:
        print("Recording started...")
        record_screen(RECORD_DURATION)
        print(f"Recording saved. Looping...")
except KeyboardInterrupt:
    print("Recording stopped.")

# Release resources
out.release()
cv2.destroyAllWindows()