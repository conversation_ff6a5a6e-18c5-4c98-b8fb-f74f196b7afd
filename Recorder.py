import cv2
import numpy as np
import pyautogui
import pyaudio
import wave
import threading
import time
import os
from datetime import datetime
from typing import Optional, Tuple, Union
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ScreenRecorder:
    """
    A comprehensive screen recorder class that supports video and audio recording
    with configurable settings and proper error handling.
    """

    def __init__(self,
                 fps: float = 20.0,
                 output_format: str = "mp4",
                 video_codec: str = "mp4v",
                 audio_enabled: bool = True,
                 show_mouse_clicks: bool = True,
                 show_preview: bool = False):
        """
        Initialize the ScreenRecorder.

        Args:
            fps: Frames per second for video recording
            output_format: Output video format ('mp4' or 'avi')
            video_codec: Video codec to use ('mp4v', 'XVID', 'MJPG')
            audio_enabled: Whether to record audio
            show_mouse_clicks: Whether to highlight mouse clicks
            show_preview: Whether to show live preview window
        """
        self.fps = fps
        self.output_format = output_format.lower()
        self.video_codec = video_codec
        self.audio_enabled = audio_enabled
        self.show_mouse_clicks = show_mouse_clicks
        self.show_preview = show_preview

        # Recording state
        self.is_recording = False
        self.is_paused = False
        self.video_writer = None
        self.audio_frames = []
        self.audio_stream = None
        self.audio_device = None

        # Threading
        self.video_thread = None
        self.audio_thread = None
        self.stop_event = threading.Event()

        # Recording region (None means full screen)
        self.recording_region = None

        # Audio settings
        self.audio_format = pyaudio.paInt16
        self.audio_channels = 2
        self.audio_rate = 44100
        self.audio_chunk = 1024

        # Initialize PyAudio for audio recording
        if self.audio_enabled:
            try:
                self.audio_device = pyaudio.PyAudio()
                logger.info("Audio recording initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize audio recording: {e}")
                self.audio_enabled = False

    def set_recording_region(self, region: Optional[Tuple[int, int, int, int]] = None):
        """
        Set the recording region.

        Args:
            region: Tuple of (left, top, width, height) or None for full screen
        """
        if region is not None:
            left, top, width, height = region
            if left < 0 or top < 0 or width <= 0 or height <= 0:
                raise ValueError("Invalid recording region coordinates")

        self.recording_region = region
        logger.info(f"Recording region set to: {region if region else 'Full screen'}")

    def _get_screen_size(self) -> Tuple[int, int]:
        """Get the screen size for recording."""
        if self.recording_region:
            return (self.recording_region[2], self.recording_region[3])
        else:
            return pyautogui.size()

    def _capture_frame(self) -> np.ndarray:
        """Capture a single frame from the screen."""
        try:
            if self.recording_region:
                left, top, width, height = self.recording_region
                img = pyautogui.screenshot(region=(left, top, width, height))
            else:
                img = pyautogui.screenshot()

            frame = np.array(img)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

            # Add mouse click indicator if enabled
            if self.show_mouse_clicks:
                mouse_x, mouse_y = pyautogui.position()

                # Adjust mouse coordinates for recording region
                if self.recording_region:
                    left, top, _, _ = self.recording_region
                    mouse_x -= left
                    mouse_y -= top

                    # Only show click if mouse is within recording region
                    if 0 <= mouse_x < self.recording_region[2] and 0 <= mouse_y < self.recording_region[3]:
                        if pyautogui.mouseDown():
                            cv2.circle(frame, (mouse_x, mouse_y), 15, (0, 0, 255), -1)
                            cv2.circle(frame, (mouse_x, mouse_y), 20, (255, 255, 255), 2)
                else:
                    if pyautogui.mouseDown():
                        cv2.circle(frame, (mouse_x, mouse_y), 15, (0, 0, 255), -1)
                        cv2.circle(frame, (mouse_x, mouse_y), 20, (255, 255, 255), 2)

            return frame

        except Exception as e:
            logger.error(f"Error capturing frame: {e}")
            return None

    def _audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio recording."""
        if self.is_recording and not self.is_paused:
            self.audio_frames.append(in_data)
        return (in_data, pyaudio.paContinue)

    def _video_recording_loop(self, output_path: str):
        """Main video recording loop running in a separate thread."""
        try:
            screen_size = self._get_screen_size()
            fourcc = cv2.VideoWriter_fourcc(*self.video_codec)
            self.video_writer = cv2.VideoWriter(output_path, fourcc, self.fps, screen_size)

            if not self.video_writer.isOpened():
                raise RuntimeError(f"Failed to open video writer for {output_path}")

            logger.info(f"Video recording started: {output_path}")
            frame_time = 1.0 / self.fps

            while self.is_recording and not self.stop_event.is_set():
                if not self.is_paused:
                    start_time = time.time()

                    frame = self._capture_frame()
                    if frame is not None:
                        self.video_writer.write(frame)

                        # Show preview if enabled
                        if self.show_preview:
                            cv2.imshow("Screen Recording Preview", frame)
                            if cv2.waitKey(1) & 0xFF == ord('q'):
                                self.stop_recording()
                                break

                    # Maintain frame rate
                    elapsed = time.time() - start_time
                    sleep_time = max(0, frame_time - elapsed)
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                else:
                    time.sleep(0.1)  # Sleep while paused

        except Exception as e:
            logger.error(f"Error in video recording loop: {e}")
        finally:
            if self.video_writer:
                self.video_writer.release()
            if self.show_preview:
                cv2.destroyAllWindows()

    def _audio_recording_loop(self):
        """Audio recording loop running in a separate thread."""
        try:
            if not self.audio_device:
                return

            self.audio_stream = self.audio_device.open(
                format=self.audio_format,
                channels=self.audio_channels,
                rate=self.audio_rate,
                input=True,
                frames_per_buffer=self.audio_chunk,
                stream_callback=self._audio_callback
            )

            self.audio_stream.start_stream()
            logger.info("Audio recording started")

            while self.is_recording and not self.stop_event.is_set():
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"Error in audio recording: {e}")
        finally:
            if self.audio_stream:
                self.audio_stream.stop_stream()
                self.audio_stream.close()

    def start_recording(self, output_path: str = None) -> bool:
        """
        Start screen recording.

        Args:
            output_path: Path to save the recording. If None, auto-generates filename.

        Returns:
            bool: True if recording started successfully, False otherwise
        """
        if self.is_recording:
            logger.warning("Recording is already in progress")
            return False

        try:
            # Generate output path if not provided
            if output_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"screen_recording_{timestamp}.{self.output_format}"

            # Ensure output directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Reset state
            self.is_recording = True
            self.is_paused = False
            self.stop_event.clear()
            self.audio_frames = []

            # Start video recording thread
            self.video_thread = threading.Thread(
                target=self._video_recording_loop,
                args=(output_path,),
                daemon=True
            )
            self.video_thread.start()

            # Start audio recording thread if enabled
            if self.audio_enabled and self.audio_device:
                self.audio_thread = threading.Thread(
                    target=self._audio_recording_loop,
                    daemon=True
                )
                self.audio_thread.start()

            logger.info(f"Screen recording started successfully: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to start recording: {e}")
            self.is_recording = False
            return False

    def stop_recording(self, save_audio: bool = True) -> bool:
        """
        Stop screen recording.

        Args:
            save_audio: Whether to save audio to a separate file

        Returns:
            bool: True if recording stopped successfully, False otherwise
        """
        if not self.is_recording:
            logger.warning("No recording in progress")
            return False

        try:
            logger.info("Stopping screen recording...")

            # Signal threads to stop
            self.is_recording = False
            self.stop_event.set()

            # Wait for threads to finish
            if self.video_thread and self.video_thread.is_alive():
                self.video_thread.join(timeout=5.0)

            if self.audio_thread and self.audio_thread.is_alive():
                self.audio_thread.join(timeout=5.0)

            # Save audio if enabled and requested
            if save_audio and self.audio_enabled and self.audio_frames:
                self._save_audio()

            logger.info("Screen recording stopped successfully")
            return True

        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            return False

    def pause_recording(self):
        """Pause the current recording."""
        if self.is_recording and not self.is_paused:
            self.is_paused = True
            logger.info("Recording paused")
        else:
            logger.warning("Cannot pause: no active recording or already paused")

    def resume_recording(self):
        """Resume a paused recording."""
        if self.is_recording and self.is_paused:
            self.is_paused = False
            logger.info("Recording resumed")
        else:
            logger.warning("Cannot resume: no active recording or not paused")

    def _save_audio(self):
        """Save recorded audio to a WAV file."""
        try:
            if not self.audio_frames:
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            audio_filename = f"screen_recording_audio_{timestamp}.wav"

            with wave.open(audio_filename, 'wb') as wf:
                wf.setnchannels(self.audio_channels)
                wf.setsampwidth(self.audio_device.get_sample_size(self.audio_format))
                wf.setframerate(self.audio_rate)
                wf.writeframes(b''.join(self.audio_frames))

            logger.info(f"Audio saved to: {audio_filename}")

        except Exception as e:
            logger.error(f"Error saving audio: {e}")

    def get_recording_status(self) -> dict:
        """
        Get current recording status.

        Returns:
            dict: Status information including recording state, settings, etc.
        """
        return {
            'is_recording': self.is_recording,
            'is_paused': self.is_paused,
            'fps': self.fps,
            'output_format': self.output_format,
            'video_codec': self.video_codec,
            'audio_enabled': self.audio_enabled,
            'show_mouse_clicks': self.show_mouse_clicks,
            'show_preview': self.show_preview,
            'recording_region': self.recording_region,
            'screen_size': self._get_screen_size()
        }

    def cleanup(self):
        """Clean up resources and stop recording if active."""
        try:
            if self.is_recording:
                self.stop_recording()

            if self.audio_device:
                self.audio_device.terminate()
                self.audio_device = None

            if self.show_preview:
                cv2.destroyAllWindows()

            logger.info("ScreenRecorder cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()


# Example usage and demonstration
def main():
    """
    Demonstration of the ScreenRecorder class usage.
    """
    print("Screen Recorder Demo")
    print("===================")

    # Create recorder with custom settings
    recorder = ScreenRecorder(
        fps=30.0,
        output_format="mp4",
        video_codec="mp4v",
        audio_enabled=True,
        show_mouse_clicks=True,
        show_preview=False
    )

    try:
        # Display current status
        status = recorder.get_recording_status()
        print(f"Screen size: {status['screen_size']}")
        print(f"Audio enabled: {status['audio_enabled']}")

        # Start recording
        print("\nStarting recording...")
        if recorder.start_recording("demo_recording.mp4"):
            print("Recording started successfully!")
            print("Recording for 10 seconds...")

            # Record for 10 seconds
            time.sleep(10)

            # Stop recording
            print("Stopping recording...")
            recorder.stop_recording()
            print("Recording completed!")
        else:
            print("Failed to start recording")

    except KeyboardInterrupt:
        print("\nRecording interrupted by user")

    finally:
        # Clean up
        recorder.cleanup()
        print("Demo completed")


if __name__ == "__main__":
    main()