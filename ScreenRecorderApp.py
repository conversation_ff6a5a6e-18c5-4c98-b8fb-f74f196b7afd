#!/usr/bin/env python3
"""
Professional Screen Recorder Application with GUI
A comprehensive screen recording application with modern interface and advanced features.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import os
import json
from datetime import datetime, timedelta
import cv2
import numpy as np
import pyautogui
import pyaudio
import wave
import psutil
from typing import Optional, Tuple
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screen_recorder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AdvancedScreenRecorder:
    """Enhanced screen recorder with better performance and features."""

    def __init__(self):
        self.is_recording = False
        self.is_paused = False
        self.start_time = None
        self.pause_time = 0
        self.total_pause_time = 0

        # Video settings
        self.fps = 30.0
        self.quality = "High"
        self.output_format = "mp4"
        self.video_codec = "mp4v"

        # Audio settings
        self.audio_enabled = True
        self.audio_quality = "High"
        self.audio_device = None
        self.audio_stream = None
        self.audio_frames = []

        # Recording settings
        self.recording_region = None
        self.show_cursor = True
        self.show_clicks = True
        self.output_directory = os.path.expanduser("~/Desktop")

        # Threading
        self.video_thread = None
        self.audio_thread = None
        self.stop_event = threading.Event()

        # Video writer
        self.video_writer = None

        # Performance monitoring
        self.frame_count = 0
        self.dropped_frames = 0

        self._initialize_audio()

    def _initialize_audio(self):
        """Initialize audio recording."""
        if self.audio_enabled:
            try:
                self.audio_device = pyaudio.PyAudio()
                logger.info("Audio system initialized")
            except Exception as e:
                logger.warning(f"Audio initialization failed: {e}")
                self.audio_enabled = False

    def get_quality_settings(self, quality: str) -> dict:
        """Get video quality settings."""
        settings = {
            "Low": {"fps": 15, "scale": 0.5, "bitrate": 1000},
            "Medium": {"fps": 24, "scale": 0.75, "bitrate": 2500},
            "High": {"fps": 30, "scale": 1.0, "bitrate": 5000},
            "Ultra": {"fps": 60, "scale": 1.0, "bitrate": 8000}
        }
        return settings.get(quality, settings["High"])

    def set_recording_region(self, region: Optional[Tuple[int, int, int, int]]):
        """Set recording region."""
        self.recording_region = region
        logger.info(f"Recording region: {region if region else 'Full screen'}")

    def start_recording(self, output_path: str) -> bool:
        """Start recording with enhanced performance."""
        if self.is_recording:
            return False

        try:
            # Reset counters
            self.frame_count = 0
            self.dropped_frames = 0
            self.start_time = time.time()
            self.total_pause_time = 0

            # Get quality settings
            quality_settings = self.get_quality_settings(self.quality)
            self.fps = quality_settings["fps"]

            # Setup recording
            self.is_recording = True
            self.is_paused = False
            self.stop_event.clear()
            self.audio_frames = []

            # Start threads
            self.video_thread = threading.Thread(
                target=self._video_recording_loop,
                args=(output_path, quality_settings),
                daemon=True
            )
            self.video_thread.start()

            if self.audio_enabled:
                self.audio_thread = threading.Thread(
                    target=self._audio_recording_loop,
                    daemon=True
                )
                self.audio_thread.start()

            logger.info(f"Recording started: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to start recording: {e}")
            self.is_recording = False
            return False

    def _video_recording_loop(self, output_path: str, quality_settings: dict):
        """Enhanced video recording loop."""
        try:
            # Get screen dimensions
            if self.recording_region:
                width, height = self.recording_region[2], self.recording_region[3]
            else:
                screen_size = pyautogui.size()
                width, height = screen_size

            # Apply scaling
            scale = quality_settings["scale"]
            width = int(width * scale)
            height = int(height * scale)

            # Initialize video writer with better codec
            if self.output_format == "mp4":
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            else:
                fourcc = cv2.VideoWriter_fourcc(*'XVID')

            self.video_writer = cv2.VideoWriter(
                output_path, fourcc, self.fps, (width, height)
            )

            if not self.video_writer.isOpened():
                raise RuntimeError("Failed to initialize video writer")

            frame_interval = 1.0 / self.fps
            last_frame_time = time.time()

            while self.is_recording and not self.stop_event.is_set():
                if not self.is_paused:
                    current_time = time.time()

                    # Capture frame
                    frame = self._capture_enhanced_frame(scale)
                    if frame is not None:
                        self.video_writer.write(frame)
                        self.frame_count += 1
                    else:
                        self.dropped_frames += 1

                    # Frame rate control
                    elapsed = current_time - last_frame_time
                    sleep_time = max(0, frame_interval - elapsed)
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                    last_frame_time = time.time()
                else:
                    time.sleep(0.1)

        except Exception as e:
            logger.error(f"Video recording error: {e}")
        finally:
            if self.video_writer:
                self.video_writer.release()

    def _capture_enhanced_frame(self, scale: float) -> Optional[np.ndarray]:
        """Capture frame with enhancements."""
        try:
            # Capture screenshot
            if self.recording_region:
                img = pyautogui.screenshot(region=self.recording_region)
            else:
                img = pyautogui.screenshot()

            # Convert to numpy array
            frame = np.array(img)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

            # Apply scaling
            if scale != 1.0:
                new_width = int(frame.shape[1] * scale)
                new_height = int(frame.shape[0] * scale)
                frame = cv2.resize(frame, (new_width, new_height))

            # Add cursor if enabled
            if self.show_cursor:
                self._add_cursor_to_frame(frame, scale)

            return frame

        except Exception as e:
            logger.error(f"Frame capture error: {e}")
            return None

    def _add_cursor_to_frame(self, frame: np.ndarray, scale: float):
        """Add cursor to frame."""
        try:
            mouse_x, mouse_y = pyautogui.position()

            # Adjust for recording region
            if self.recording_region:
                mouse_x -= self.recording_region[0]
                mouse_y -= self.recording_region[1]

            # Apply scaling
            mouse_x = int(mouse_x * scale)
            mouse_y = int(mouse_y * scale)

            # Check bounds
            if 0 <= mouse_x < frame.shape[1] and 0 <= mouse_y < frame.shape[0]:
                # Draw cursor
                cv2.circle(frame, (mouse_x, mouse_y), 8, (255, 255, 255), -1)
                cv2.circle(frame, (mouse_x, mouse_y), 8, (0, 0, 0), 2)

                # Show clicks
                if self.show_clicks and pyautogui.mouseDown():
                    cv2.circle(frame, (mouse_x, mouse_y), 15, (0, 0, 255), 3)

        except Exception as e:
            logger.debug(f"Cursor drawing error: {e}")

    def _audio_recording_loop(self):
        """Enhanced audio recording."""
        try:
            # Audio quality settings
            if self.audio_quality == "High":
                rate, channels = 44100, 2
            else:
                rate, channels = 22050, 1

            self.audio_stream = self.audio_device.open(
                format=pyaudio.paInt16,
                channels=channels,
                rate=rate,
                input=True,
                frames_per_buffer=1024
            )

            while self.is_recording and not self.stop_event.is_set():
                if not self.is_paused:
                    try:
                        data = self.audio_stream.read(1024, exception_on_overflow=False)
                        self.audio_frames.append(data)
                    except Exception as e:
                        logger.debug(f"Audio read error: {e}")
                else:
                    time.sleep(0.1)

        except Exception as e:
            logger.error(f"Audio recording error: {e}")
        finally:
            if self.audio_stream:
                self.audio_stream.stop_stream()
                self.audio_stream.close()

    def pause_recording(self):
        """Pause recording."""
        if self.is_recording and not self.is_paused:
            self.is_paused = True
            self.pause_time = time.time()
            logger.info("Recording paused")

    def resume_recording(self):
        """Resume recording."""
        if self.is_recording and self.is_paused:
            self.is_paused = False
            if self.pause_time:
                self.total_pause_time += time.time() - self.pause_time
            logger.info("Recording resumed")

    def stop_recording(self) -> bool:
        """Stop recording."""
        if not self.is_recording:
            return False

        try:
            self.is_recording = False
            self.stop_event.set()

            # Wait for threads
            if self.video_thread:
                self.video_thread.join(timeout=5)
            if self.audio_thread:
                self.audio_thread.join(timeout=5)

            # Save audio if available
            if self.audio_frames:
                self._save_audio()

            logger.info("Recording stopped successfully")
            return True

        except Exception as e:
            logger.error(f"Stop recording error: {e}")
            return False

    def _save_audio(self):
        """Save audio to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            audio_path = os.path.join(self.output_directory, f"audio_{timestamp}.wav")

            with wave.open(audio_path, 'wb') as wf:
                wf.setnchannels(2 if self.audio_quality == "High" else 1)
                wf.setsampwidth(self.audio_device.get_sample_size(pyaudio.paInt16))
                wf.setframerate(44100 if self.audio_quality == "High" else 22050)
                wf.writeframes(b''.join(self.audio_frames))

            logger.info(f"Audio saved: {audio_path}")

        except Exception as e:
            logger.error(f"Audio save error: {e}")

    def get_recording_stats(self) -> dict:
        """Get recording statistics."""
        if self.start_time:
            elapsed = time.time() - self.start_time - self.total_pause_time
            if self.is_paused and self.pause_time:
                elapsed -= (time.time() - self.pause_time)
        else:
            elapsed = 0

        return {
            'duration': elapsed,
            'frame_count': self.frame_count,
            'dropped_frames': self.dropped_frames,
            'fps': self.frame_count / elapsed if elapsed > 0 else 0,
            'is_recording': self.is_recording,
            'is_paused': self.is_paused
        }

    def cleanup(self):
        """Clean up resources."""
        try:
            if self.is_recording:
                self.stop_recording()

            if self.audio_device:
                self.audio_device.terminate()

        except Exception as e:
            logger.error(f"Cleanup error: {e}")


class ScreenRecorderGUI:
    """Professional GUI for the screen recorder."""

    def __init__(self):
        self.recorder = AdvancedScreenRecorder()
        self.root = tk.Tk()
        self.setup_gui()
        self.update_timer = None
        self.recording_start_time = None

        # Load settings
        self.load_settings()

        # Setup update loop
        self.update_display()

    def setup_gui(self):
        """Setup the GUI interface."""
        self.root.title("Professional Screen Recorder")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # Configure style
        style = ttk.Style()
        style.theme_use('clam')

        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="Professional Screen Recorder",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Recording controls
        self.setup_recording_controls(main_frame)

        # Settings
        self.setup_settings(main_frame)

        # Status and statistics
        self.setup_status_display(main_frame)

        # Output settings
        self.setup_output_settings(main_frame)

    def setup_recording_controls(self, parent):
        """Setup recording control buttons."""
        controls_frame = ttk.LabelFrame(parent, text="Recording Controls", padding="10")
        controls_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Control buttons
        self.start_btn = ttk.Button(controls_frame, text="Start Recording",
                                   command=self.start_recording, style='Accent.TButton')
        self.start_btn.grid(row=0, column=0, padx=(0, 5))

        self.pause_btn = ttk.Button(controls_frame, text="Pause",
                                   command=self.pause_recording, state='disabled')
        self.pause_btn.grid(row=0, column=1, padx=5)

        self.stop_btn = ttk.Button(controls_frame, text="Stop Recording",
                                  command=self.stop_recording, state='disabled')
        self.stop_btn.grid(row=0, column=2, padx=(5, 0))

        # Recording status
        self.status_var = tk.StringVar(value="Ready to record")
        status_label = ttk.Label(controls_frame, textvariable=self.status_var,
                                font=('Arial', 10, 'bold'))
        status_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))

    def setup_settings(self, parent):
        """Setup recording settings."""
        settings_frame = ttk.LabelFrame(parent, text="Recording Settings", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)

        # Quality setting
        ttk.Label(settings_frame, text="Quality:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.quality_var = tk.StringVar(value="High")
        quality_combo = ttk.Combobox(settings_frame, textvariable=self.quality_var,
                                    values=["Low", "Medium", "High", "Ultra"], state="readonly")
        quality_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        quality_combo.bind('<<ComboboxSelected>>', self.on_quality_change)

        # Format setting
        ttk.Label(settings_frame, text="Format:").grid(row=0, column=2, sticky=tk.W, padx=(10, 10))
        self.format_var = tk.StringVar(value="mp4")
        format_combo = ttk.Combobox(settings_frame, textvariable=self.format_var,
                                   values=["mp4", "avi"], state="readonly")
        format_combo.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # Audio settings
        self.audio_var = tk.BooleanVar(value=True)
        audio_check = ttk.Checkbutton(settings_frame, text="Record Audio",
                                     variable=self.audio_var, command=self.on_audio_change)
        audio_check.grid(row=1, column=0, sticky=tk.W, pady=(10, 0))

        # Cursor settings
        self.cursor_var = tk.BooleanVar(value=True)
        cursor_check = ttk.Checkbutton(settings_frame, text="Show Cursor",
                                      variable=self.cursor_var, command=self.on_cursor_change)
        cursor_check.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))

        # Click visualization
        self.clicks_var = tk.BooleanVar(value=True)
        clicks_check = ttk.Checkbutton(settings_frame, text="Show Clicks",
                                      variable=self.clicks_var, command=self.on_clicks_change)
        clicks_check.grid(row=1, column=2, sticky=tk.W, pady=(10, 0))

        # Region selection
        region_btn = ttk.Button(settings_frame, text="Select Region",
                               command=self.select_region)
        region_btn.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def setup_status_display(self, parent):
        """Setup status and statistics display."""
        status_frame = ttk.LabelFrame(parent, text="Recording Statistics", padding="10")
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        status_frame.columnconfigure(3, weight=1)

        # Duration
        ttk.Label(status_frame, text="Duration:").grid(row=0, column=0, sticky=tk.W)
        self.duration_var = tk.StringVar(value="00:00:00")
        ttk.Label(status_frame, textvariable=self.duration_var, font=('Courier', 10)).grid(
            row=0, column=1, sticky=tk.W, padx=(10, 20))

        # Frame rate
        ttk.Label(status_frame, text="FPS:").grid(row=0, column=2, sticky=tk.W)
        self.fps_var = tk.StringVar(value="0.0")
        ttk.Label(status_frame, textvariable=self.fps_var, font=('Courier', 10)).grid(
            row=0, column=3, sticky=tk.W, padx=(10, 0))

        # Frame count
        ttk.Label(status_frame, text="Frames:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.frames_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.frames_var, font=('Courier', 10)).grid(
            row=1, column=1, sticky=tk.W, padx=(10, 20), pady=(5, 0))

        # Dropped frames
        ttk.Label(status_frame, text="Dropped:").grid(row=1, column=2, sticky=tk.W, pady=(5, 0))
        self.dropped_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.dropped_var, font=('Courier', 10)).grid(
            row=1, column=3, sticky=tk.W, padx=(10, 0), pady=(5, 0))

    def setup_output_settings(self, parent):
        """Setup output directory settings."""
        output_frame = ttk.LabelFrame(parent, text="Output Settings", padding="10")
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)

        # Output directory
        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W)
        self.output_var = tk.StringVar(value=self.recorder.output_directory)
        output_entry = ttk.Entry(output_frame, textvariable=self.output_var, state="readonly")
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))

        browse_btn = ttk.Button(output_frame, text="Browse", command=self.browse_output_dir)
        browse_btn.grid(row=0, column=2)

        # Filename preview
        ttk.Label(output_frame, text="Filename:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.filename_var = tk.StringVar()
        self.update_filename_preview()
        ttk.Label(output_frame, textvariable=self.filename_var, font=('Courier', 9)).grid(
            row=1, column=1, columnspan=2, sticky=tk.W, padx=(10, 0), pady=(10, 0))

    def on_quality_change(self, event=None):
        """Handle quality change."""
        self.recorder.quality = self.quality_var.get()
        self.update_filename_preview()

    def on_audio_change(self):
        """Handle audio setting change."""
        self.recorder.audio_enabled = self.audio_var.get()

    def on_cursor_change(self):
        """Handle cursor setting change."""
        self.recorder.show_cursor = self.cursor_var.get()

    def on_clicks_change(self):
        """Handle clicks setting change."""
        self.recorder.show_clicks = self.clicks_var.get()

    def select_region(self):
        """Select recording region."""
        messagebox.showinfo("Region Selection",
                           "Click and drag to select recording region.\n"
                           "Press ESC to cancel or ENTER to confirm.\n"
                           "Leave empty to record full screen.")
        # For now, just reset to full screen
        self.recorder.set_recording_region(None)

    def browse_output_dir(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(initialdir=self.recorder.output_directory)
        if directory:
            self.recorder.output_directory = directory
            self.output_var.set(directory)
            self.update_filename_preview()

    def update_filename_preview(self):
        """Update filename preview."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        quality = self.quality_var.get().lower()
        format_ext = self.format_var.get()
        filename = f"recording_{quality}_{timestamp}.{format_ext}"
        self.filename_var.set(filename)

    def start_recording(self):
        """Start recording."""
        try:
            # Generate output path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            quality = self.quality_var.get().lower()
            format_ext = self.format_var.get()
            filename = f"recording_{quality}_{timestamp}.{format_ext}"
            output_path = os.path.join(self.recorder.output_directory, filename)

            # Update recorder settings
            self.recorder.quality = self.quality_var.get()
            self.recorder.output_format = self.format_var.get()
            self.recorder.audio_enabled = self.audio_var.get()
            self.recorder.show_cursor = self.cursor_var.get()
            self.recorder.show_clicks = self.clicks_var.get()

            # Start recording
            if self.recorder.start_recording(output_path):
                self.recording_start_time = time.time()
                self.status_var.set("Recording...")

                # Update button states
                self.start_btn.config(state='disabled')
                self.pause_btn.config(state='normal')
                self.stop_btn.config(state='normal')

                logger.info(f"Recording started: {output_path}")
            else:
                messagebox.showerror("Error", "Failed to start recording")

        except Exception as e:
            logger.error(f"Start recording error: {e}")
            messagebox.showerror("Error", f"Failed to start recording: {e}")

    def pause_recording(self):
        """Pause/resume recording."""
        try:
            if self.recorder.is_paused:
                self.recorder.resume_recording()
                self.pause_btn.config(text="Pause")
                self.status_var.set("Recording...")
            else:
                self.recorder.pause_recording()
                self.pause_btn.config(text="Resume")
                self.status_var.set("Paused")

        except Exception as e:
            logger.error(f"Pause recording error: {e}")
            messagebox.showerror("Error", f"Failed to pause recording: {e}")

    def stop_recording(self):
        """Stop recording."""
        try:
            if self.recorder.stop_recording():
                self.status_var.set("Recording stopped")

                # Update button states
                self.start_btn.config(state='normal')
                self.pause_btn.config(state='disabled', text="Pause")
                self.stop_btn.config(state='disabled')

                # Show completion message
                messagebox.showinfo("Recording Complete",
                                   f"Recording saved successfully!\n"
                                   f"Location: {self.recorder.output_directory}")

                logger.info("Recording stopped successfully")
            else:
                messagebox.showerror("Error", "Failed to stop recording")

        except Exception as e:
            logger.error(f"Stop recording error: {e}")
            messagebox.showerror("Error", f"Failed to stop recording: {e}")

    def update_display(self):
        """Update the display with current statistics."""
        try:
            stats = self.recorder.get_recording_stats()

            # Update duration
            duration = int(stats['duration'])
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            seconds = duration % 60
            self.duration_var.set(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

            # Update FPS
            self.fps_var.set(f"{stats['fps']:.1f}")

            # Update frame count
            self.frames_var.set(str(stats['frame_count']))

            # Update dropped frames
            self.dropped_var.set(str(stats['dropped_frames']))

            # Update status based on recording state
            if not self.recorder.is_recording:
                if stats['frame_count'] > 0:
                    self.status_var.set("Recording completed")
                else:
                    self.status_var.set("Ready to record")
            elif self.recorder.is_paused:
                self.status_var.set("Paused")
            else:
                self.status_var.set("Recording...")

        except Exception as e:
            logger.debug(f"Display update error: {e}")

        # Schedule next update
        self.root.after(1000, self.update_display)

    def load_settings(self):
        """Load settings from configuration file."""
        try:
            config_file = "screen_recorder_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                self.quality_var.set(settings.get('quality', 'High'))
                self.format_var.set(settings.get('format', 'mp4'))
                self.audio_var.set(settings.get('audio_enabled', True))
                self.cursor_var.set(settings.get('show_cursor', True))
                self.clicks_var.set(settings.get('show_clicks', True))

                output_dir = settings.get('output_directory')
                if output_dir and os.path.exists(output_dir):
                    self.recorder.output_directory = output_dir
                    self.output_var.set(output_dir)

                logger.info("Settings loaded successfully")

        except Exception as e:
            logger.warning(f"Failed to load settings: {e}")

    def save_settings(self):
        """Save current settings to configuration file."""
        try:
            settings = {
                'quality': self.quality_var.get(),
                'format': self.format_var.get(),
                'audio_enabled': self.audio_var.get(),
                'show_cursor': self.cursor_var.get(),
                'show_clicks': self.clicks_var.get(),
                'output_directory': self.recorder.output_directory
            }

            with open("screen_recorder_config.json", 'w') as f:
                json.dump(settings, f, indent=2)

            logger.info("Settings saved successfully")

        except Exception as e:
            logger.warning(f"Failed to save settings: {e}")

    def on_closing(self):
        """Handle application closing."""
        try:
            # Stop recording if active
            if self.recorder.is_recording:
                result = messagebox.askyesno("Recording Active",
                                           "Recording is in progress. Stop recording and exit?")
                if result:
                    self.recorder.stop_recording()
                else:
                    return

            # Save settings
            self.save_settings()

            # Cleanup
            self.recorder.cleanup()

            # Close application
            self.root.destroy()

        except Exception as e:
            logger.error(f"Closing error: {e}")
            self.root.destroy()

    def run(self):
        """Run the application."""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """Main application entry point."""
    try:
        # Create and run the GUI application
        app = ScreenRecorderGUI()
        app.run()

    except Exception as e:
        logger.error(f"Application error: {e}")
        messagebox.showerror("Application Error", f"An error occurred: {e}")


if __name__ == "__main__":
    main()
