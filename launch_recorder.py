#!/usr/bin/env python3
"""
Launch script for the Professional Screen Recorder.
"""

import sys
import os

def main():
    """Launch the screen recorder application."""
    try:
        print("Starting Professional Screen Recorder...")
        print("Loading GUI interface...")
        
        # Import and run the application
        from ScreenRecorderApp import ScreenRecorderGUI
        
        print("Creating application...")
        app = ScreenRecorderGUI()
        
        print("Starting GUI...")
        app.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
